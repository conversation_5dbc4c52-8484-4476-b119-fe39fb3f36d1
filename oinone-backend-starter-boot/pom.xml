<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>pro.shushi.pamirs</groupId>
        <artifactId>oinone-backend-starter</artifactId>
        <version>6.2.1</version>
    </parent>

    <artifactId>oinone-backend-starter-boot</artifactId>

    <dependencies>
        <dependency>
            <groupId>pro.shushi.pamirs</groupId>
            <artifactId>a</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.boot</groupId>
            <artifactId>pamirs-boot-modules</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.boot</groupId>
            <artifactId>pamirs-distribution-id</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.boot</groupId>
            <artifactId>pamirs-meta-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.boot</groupId>
            <artifactId>pamirs-boot-standard</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.boot</groupId>
            <artifactId>pamirs-boot-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.framework</groupId>
            <artifactId>pamirs-connectors-data-api</artifactId>
        </dependency>

        <!-- 用户 -->
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-user-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-user-view</artifactId>
        </dependency>
        <!--end-->

        <!-- auth -->
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-auth3-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-auth3-view</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-auth3-rbac-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-auth3-rbac-view</artifactId>
        </dependency>
        <!-- auth compatible -->
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-management-center</artifactId>
        </dependency>

        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-resource-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-channel-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-message-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-international</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-business-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-business-view</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-file2-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-translate</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-eip2-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-eip2-view</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-trigger-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-trigger-bridge-tbschedule</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-expression-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.framework</groupId>
            <artifactId>pamirs-connectors-event</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.framework</groupId>
            <artifactId>pamirs-connectors-event-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-apps-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-apps-view</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-sys-setting-core</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-sys-setting-view</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-my-center-view</artifactId>
        </dependency>
        <dependency>
            <groupId>pro.shushi.pamirs.core</groupId>
            <artifactId>pamirs-sql-record-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.json</groupId>
            <artifactId>jakarta.json-api</artifactId>
            <version>2.1.1</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <jvmArguments>
                                -server
                                -Xss1M
                                -Xms1G
                                -Xmx1G
                                -XX:+UnlockExperimentalVMOptions
                                -XX:+UseG1GC
                                -XX:+UseNUMA
                                -XX:+UseCompressedOops
                                -XX:+ParallelRefProcEnabled
                                -Xdebug
                                -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=18881
                            </jvmArguments>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-install-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-deploy-plugin</artifactId>
                        <configuration>
                            <skip>true</skip>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>