<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>pro.shushi.pamirs</groupId>
    <artifactId>oinone-backend-starter</artifactId>
    <version>6.2.1</version>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.resources.sourceEncoding>UTF-8</project.resources.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.resources.sourceEncoding>UTF-8</project.resources.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <java.version>1.8</java.version>
        <maven.compiler.version>3.14.0</maven.compiler.version>
        <maven.surefire.version>2.22.2</maven.surefire.version>
        <maven.source.version>3.2.1</maven.source.version>
        <maven.resources.version>3.3.1</maven.resources.version>
        <maven.shade.version>3.6.0</maven.shade.version>
        <maven.versions.version>2.18.0</maven.versions.version>
        <maven.jar.version>3.4.2</maven.jar.version>
        <maven.deploy.version>3.1.4</maven.deploy.version>
        <maven.install.version>3.1.4</maven.install.version>
        <maven.enforcer.version>3.5.0</maven.enforcer.version>

        <spring.boot.version>2.3.8.RELEASE</spring.boot.version>
        <elasticsearch.version>8.4.1</elasticsearch.version>

        <!-- 平台基础 -->
        <pamirs.middleware.version>6.2.1</pamirs.middleware.version>
        <pamirs.k2.version>6.2.1</pamirs.k2.version>
        <pamirs.framework.version>6.2.1</pamirs.framework.version>
        <pamirs.boot.version>6.2.1</pamirs.boot.version>

        <!-- 平台功能 -->
        <pamirs.core.version>6.2.1</pamirs.core.version>
    </properties>

    <modules>
        <module>oinone-backend-starter-boot</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>pro.shushi.pamirs</groupId>
                <artifactId>pamirs-k2</artifactId>
                <version>${pamirs.k2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>pro.shushi.pamirs</groupId>
                <artifactId>pamirs-framework</artifactId>
                <version>${pamirs.framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>pro.shushi.pamirs.boot</groupId>
                <artifactId>pamirs-boot-dependencies</artifactId>
                <version>${pamirs.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>pro.shushi.pamirs.core</groupId>
                <artifactId>pamirs-core-dependencies</artifactId>
                <version>${pamirs.core.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>pro.shushi.pamirs.middleware</groupId>
                <artifactId>pamirs-middleware-dependencies</artifactId>
                <version>${pamirs.middleware.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>${maven.install.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>${maven.deploy.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>${maven.enforcer.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven.resources.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>versions-maven-plugin</artifactId>
                    <version>${maven.versions.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgument>-parameters</compilerArgument>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.source}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.6.0,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>[${java.version},)</version>
                                </requireJavaVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <configuration>
                    <ignoredVersions>.*-M.*,.*alpha.*,.*Alpha.*,.*RC.*,.*CR.*,.*rc.*,.*-beta.*,.*Beta.*,5.4.*,6.0.*</ignoredVersions>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>